"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import MobileNav from "@/components/mobile-nav"
import EventCard from "@/components/event-card"
import { useMediaQuery } from "@/hooks/use-media-query"
import CartModal from "@/components/cart-modal"
import AuthModal from "@/components/auth-modal"
import { useCart } from "@/context/cart-context"
import { Search, Filter, Loader2 } from "lucide-react"
import eventManager from "@/lib/eventManager"

export default function EventsPage() {
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [authMode, setAuthMode] = useState("login")
  const [activeFilter, setActiveFilter] = useState("upcoming")
  const [selectedGenres, setSelectedGenres] = useState([])
  const [selectedLocations, setSelectedLocations] = useState([])
  const [searchQuery, setSearchQuery] = useState("")
  const [itemsPerPage, setItemsPerPage] = useState(12)
  const [showFilters, setShowFilters] = useState(false)

  // API state
  const [events, setEvents] = useState([])
  const [genres, setGenres] = useState([])
  const [availableLocations, setAvailableLocations] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    totalPages: 0
  })

  const isMobile = useMediaQuery("(max-width: 768px)")
  const { isCartOpen } = useCart()

  // Load initial data
  useEffect(() => {
    loadInitialData()
  }, [])

  // Load data when filters change (frontend-based filtering)
  useEffect(() => {
    if (eventManager.dataLoaded) {
      applyFilters()
    }
  }, [activeFilter, selectedGenres, selectedLocations, searchQuery, itemsPerPage])

  const loadInitialData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Load all data in parallel
      await Promise.all([
        loadGenres(),
        loadLocations(),
        loadAllEvents()
      ])
    } catch (err) {
      console.error('Error loading initial data:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const loadGenres = async () => {
    try {
      const genresData = await eventManager.loadGenres()
      setGenres(genresData)
    } catch (err) {
      console.error('Error loading genres:', err)
    }
  }

  const loadLocations = async () => {
    try {
      const locationsData = await eventManager.loadLocations()
      setAvailableLocations(locationsData)
    } catch (err) {
      console.error('Error loading locations:', err)
    }
  }

  const loadAllEvents = async () => {
    try {
      const result = await eventManager.loadAllEvents()
      setEvents(result.events)
      setPagination(result.pagination)

      // Apply initial filters
      applyFilters()
    } catch (err) {
      console.error('Error loading events:', err)
      setError(err.message)
    }
  }

  const applyFilters = () => {
    // Update filters in event manager
    eventManager.updateFilters({
      status: activeFilter,
      genreIds: selectedGenres,
      locationIds: selectedLocations,
      search: searchQuery
    })

    // Update pagination
    eventManager.updatePagination({
      page: 1,
      limit: itemsPerPage
    })

    // Get filtered results
    const result = eventManager.getEvents()
    if (result && result.then) {
      // If it's a promise (first load)
      result.then(data => {
        setEvents(data.events)
        setPagination(data.pagination)
      })
    } else {
      // If it's immediate data (subsequent filters)
      setEvents(result.events)
      setPagination(result.pagination)
    }
  }

  const openAuthModal = (mode) => {
    setAuthMode(mode)
    setShowAuthModal(true)
  }

  const toggleGenre = (genreId) => {
    if (selectedGenres.includes(genreId)) {
      setSelectedGenres(selectedGenres.filter((g) => g !== genreId))
    } else {
      setSelectedGenres([genreId]) // Only allow one genre selection for simplicity
    }
  }

  const toggleLocation = (locationId) => {
    if (selectedLocations.includes(locationId)) {
      setSelectedLocations(selectedLocations.filter((l) => l !== locationId))
    } else {
      setSelectedLocations([locationId]) // Only allow one location selection for simplicity
    }
  }

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value)
  }

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value))
  }

  const handleFilterChange = (filter) => {
    setActiveFilter(filter)
  }

  const handlePageChange = (newPage) => {
    eventManager.changePage(newPage)
    const result = eventManager.getEvents()
    setEvents(result.events)
    setPagination(result.pagination)
  }

  // Events are already filtered by the API, so we just use them directly
  const filteredEvents = events

  return (
    <div className="min-h-screen bg-zinc-950 text-white">
      <Navbar onLoginClick={() => openAuthModal("login")} onRegisterClick={() => openAuthModal("register")} />

      <main className="container mx-auto px-4 pt-24 pb-20 md:pb-8">
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }}>
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
            <h1 className="text-5xl font-bold mb-4 md:mb-0">All Events</h1>

            <div className="w-full md:w-auto flex flex-col md:flex-row gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-zinc-400" size={18} />
                <input
                  type="text"
                  placeholder="Search events..."
                  className="bg-zinc-800 rounded-full pl-10 pr-4 py-2 w-full md:w-64 focus:outline-none focus:ring-2 focus:ring-red-500"
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
              </div>

              <button
                className="md:hidden flex items-center gap-2 bg-zinc-800 rounded-full px-4 py-2"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter size={18} />
                Filters
              </button>

              <select
                className="bg-zinc-800 rounded-full remx-0.25 remy-0.125 focus:outline-none focus:ring-2 focus:ring-red-500"
                value={itemsPerPage}
                onChange={handleItemsPerPageChange}
              >
                <option value={12}>12 per page</option>
                <option value={24}>24 per page</option>
                <option value={36}>36 per page</option>
              </select>
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-8">
            <aside className={`w-full md:w-64 md:block ${showFilters ? "block" : "hidden"}`}>
              <div className="bg-zinc-900 rounded-lg p-4 mb-6">
                <h3 className="font-semibold mb-4">Event Status</h3>
                <div className="flex flex-wrap gap-2">
                  {["live", "upcoming", "past"].map((filter) => (
                    <button
                      key={filter}
                      className={`px-4 py-2 rounded-full text-sm ${
                        activeFilter === filter
                          ? "bg-red-600 text-white"
                          : "bg-zinc-800 text-zinc-300 hover:bg-zinc-700"
                      }`}
                      onClick={() => handleFilterChange(filter)}
                    >
                      {filter.charAt(0).toUpperCase() + filter.slice(1)}
                    </button>
                  ))}
                </div>
              </div>

              <div className="bg-zinc-900 rounded-lg p-4 mb-6">
                <h3 className="font-semibold mb-4">Genres</h3>
                <div className="space-y-2">
                  {genres.map((genre) => (
                    <div key={genre.genre_id} className="flex items-center">
                      <input
                        type="checkbox"
                        id={`genre-${genre.genre_id}`}
                        className="w-4 h-4 rounded border-zinc-600 text-red-600 focus:ring-red-500"
                        checked={selectedGenres.includes(genre.genre_id)}
                        onChange={() => toggleGenre(genre.genre_id)}
                      />
                      <label htmlFor={`genre-${genre.genre_id}`} className="ml-2 text-sm">
                        {genre.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-zinc-900 rounded-lg p-4">
                <h3 className="font-semibold mb-4">Locations</h3>
                <div className="space-y-2">
                  {availableLocations.map((location) => (
                    <div key={location.location_id} className="flex items-center">
                      <input
                        type="checkbox"
                        id={`location-${location.location_id}`}
                        className="w-4 h-4 rounded border-zinc-600 text-red-600 focus:ring-red-500"
                        checked={selectedLocations.includes(location.location_id)}
                        onChange={() => toggleLocation(location.location_id)}
                      />
                      <label htmlFor={`location-${location.location_id}`} className="ml-2 text-sm">
                        {location.city}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </aside>

            <div className="flex-1">
              {loading ? (
                <div className="flex justify-center items-center py-20">
                  <Loader2 className="animate-spin h-8 w-8 text-red-500" />
                  <span className="ml-2 text-zinc-400">Loading events...</span>
                </div>
              ) : error ? (
                <div className="bg-zinc-900 rounded-lg p-8 text-center">
                  <h3 className="text-xl font-semibold mb-2 text-red-500">Error</h3>
                  <p className="text-zinc-400 mb-6">{error}</p>
                  <button
                    onClick={loadInitialData}
                    className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-full transition-colors"
                  >
                    Try Again
                  </button>
                </div>
              ) : filteredEvents.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredEvents.map((event) => (
                    <EventCard key={event.id} event={event} />
                  ))}
                </div>
              ) : (
                <div className="bg-zinc-900 rounded-lg p-8 text-center">
                  <h3 className="text-xl font-semibold mb-2">No events found</h3>
                  <p className="text-zinc-400 mb-6">Try adjusting your filters or search query</p>
                </div>
              )}

              {pagination.totalPages > 1 && (
                <div className="mt-8 flex justify-center">
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page <= 1}
                      className="bg-zinc-800 hover:bg-zinc-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-full transition-colors"
                    >
                      Previous
                    </button>
                    <span className="text-zinc-400 px-4">
                      Page {pagination.page} of {pagination.totalPages}
                    </span>
                    <button
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page >= pagination.totalPages}
                      className="bg-zinc-800 hover:bg-zinc-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-full transition-colors"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </main>

      <Footer />

      {isMobile && <MobileNav />}

      {isCartOpen && <CartModal />}

      {showAuthModal && (
        <AuthModal
          mode={authMode}
          onClose={() => setShowAuthModal(false)}
          onSwitchMode={() => setAuthMode(authMode === "login" ? "register" : "login")}
        />
      )}
    </div>
  )
}
