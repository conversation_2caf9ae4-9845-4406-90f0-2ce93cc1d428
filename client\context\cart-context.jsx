"use client"

import { createContext, useState, useContext, useEffect } from "react"
import { cartAPI } from "@/lib/api"
import { useAuth } from "@/context/auth-context"
import { toast } from "sonner"

const CartContext = createContext(null)

export const CartProvider = ({ children }) => {
  const [cart, setCart] = useState([])
  const [isCartOpen, setIsCartOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const { user } = useAuth()

  // Load cart from backend when user is authenticated
  useEffect(() => {
    if (user) {
      console.log(user)
      loadCartFromBackend()
    } else {
      // Clear cart when user logs out
      setCart([])
    }
  }, [user])

  const loadCartFromBackend = async () => {
    try {
      setLoading(true)
      const response = await cartAPI.getCartItems()
      if (response.success) {
        setCart(response.data || [])
      }
    } catch (error) {
      console.error('Error loading cart:', error)
      // If user is not authenticated, clear cart
      if (error.response?.status === 401) {
        setCart([])
      }
    } finally {
      setLoading(false)
    }
  }

  const addToCart = async (item) => {
    if (!user) {
      toast.error("Please login to add items to cart")
      return { success: false, message: "Authentication required" }
    }

    try {
      setLoading(true)

      // For backend API, we need ticketTypeId instead of the item object
      // This will need to be passed from the component that calls addToCart
      const { ticketTypeId, quantity = 1 } = item

      if (!ticketTypeId) {
        toast.error("Invalid ticket type")
        return { success: false, message: "Invalid ticket type" }
      }

      const response = await cartAPI.addToCart(ticketTypeId, quantity)

      if (response.success) {
        // Reload cart from backend to get updated data
        await loadCartFromBackend()
        toast.success("Item added to cart successfully")
        return { success: true }
      } else {
        toast.error(response.message || "Failed to add item to cart")
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      const message = error.response?.data?.message || "Failed to add item to cart"
      toast.error(message)
      return { success: false, message }
    } finally {
      setLoading(false)
    }
  }

  const removeFromCart = async (cartId) => {
    if (!user) {
      toast.error("Please login to manage cart")
      return { success: false, message: "Authentication required" }
    }

    try {
      setLoading(true)
      const response = await cartAPI.removeFromCart(cartId)

      if (response.success) {
        // Reload cart from backend to get updated data
        await loadCartFromBackend()
        toast.success("Item removed from cart")
        return { success: true }
      } else {
        toast.error(response.message || "Failed to remove item from cart")
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('Error removing from cart:', error)
      const message = error.response?.data?.message || "Failed to remove item from cart"
      toast.error(message)
      return { success: false, message }
    } finally {
      setLoading(false)
    }
  }

  const updateQuantity = async (cartId, quantity) => {
    if (!user) {
      toast.error("Please login to manage cart")
      return { success: false, message: "Authentication required" }
    }

    try {
      setLoading(true)
      const response = await cartAPI.updateCartItemQuantity(cartId, quantity)

      if (response.success) {
        // Reload cart from backend to get updated data
        await loadCartFromBackend()
        return { success: true }
      } else {
        toast.error(response.message || "Failed to update cart item")
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('Error updating cart item:', error)
      const message = error.response?.data?.message || "Failed to update cart item"
      toast.error(message)
      return { success: false, message }
    } finally {
      setLoading(false)
    }
  }

  const clearCart = async () => {
    if (!user) {
      toast.error("Please login to manage cart")
      return { success: false, message: "Authentication required" }
    }

    try {
      setLoading(true)
      const response = await cartAPI.clearCart()

      if (response.success) {
        setCart([])
        toast.success("Cart cleared successfully")
        return { success: true }
      } else {
        toast.error(response.message || "Failed to clear cart")
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('Error clearing cart:', error)
      const message = error.response?.data?.message || "Failed to clear cart"
      toast.error(message)
      return { success: false, message }
    } finally {
      setLoading(false)
    }
  }

  const toggleCart = () => {
    setIsCartOpen(!isCartOpen)
  }

  const getCartTotal = () => {
    return cart.reduce((total, item) => total + item.price * item.quantity, 0)
  }

  const getCartCount = () => {
    return cart.reduce((count, item) => count + item.quantity, 0)
  }

  return (
    <CartContext.Provider
      value={{
        cart,
        addToCart,
        removeFromCart,
        updateQuantity,
        clearCart,
        isCartOpen,
        toggleCart,
        getCartTotal,
        getCartCount,
        loading,
        refreshCart: loadCartFromBackend,
      }}
    >
      {children}
    </CartContext.Provider>
  )
}

export const useCart = () => useContext(CartContext)
