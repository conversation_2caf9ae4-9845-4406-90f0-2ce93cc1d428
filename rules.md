# Rules
## 1. Use Axios for HTTP requests
## 2. Use React Router for navigation
## 3. Use React Context for state management
## 4. Use Prisma for database access
## 5. Use Supabase tables for authentication and authorization, update the tables as along with auth informations (check for the prisma.schema file for the masteraccounts, users, organizers & admins tables)
## 6. Must use design patterns like OOP, Singleton, Adapter, Observer etc. and best practices
## 7. Must use TypeScript
## 8. Must follow MVC architecture
## 9. use pnpm command for client and npm command for server
## 10. *** FOR ANY Media STORAGE for example, images, pdfs, videos, audio files, etc. MUST use Supabase Storage ***

## OAuth Implementation



## After each task completion, register what has been donee breifly in the Changelog.md file



