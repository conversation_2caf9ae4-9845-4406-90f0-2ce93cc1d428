const CartService = require('../services/cartService');

class CartController {
  constructor() {
    this.cartService = new CartService();
  }

  // Get user's cart items
  getCartItems = async (req, res) => {
    try {
      const accountId = req.user.accountId || req.user.id;

      if (!accountId) {
        return res.status(401).json({
          success: false,
          message: 'User authentication required',
          data: null
        });
      }

      const cartItems = await this.cartService.getCartItems(accountId);

      res.status(200).json({
        success: true,
        message: 'Cart items fetched successfully',
        data: cartItems
      });
    } catch (error) {
      console.error('Error fetching cart items:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch cart items',
        data: null
      });
    }
  };

  // Add item to cart
  addToCart = async (req, res) => {
    try {
      const accountId = req.user.accountId || req.user.id;
      const { ticketTypeId, quantity = 1 } = req.body;

      if (!accountId) {
        return res.status(401).json({
          success: false,
          message: 'User authentication required',
          data: null
        });
      }

      if (!ticketTypeId) {
        return res.status(400).json({
          success: false,
          message: 'Ticket type ID is required',
          data: null
        });
      }

      if (quantity <= 0 || !Number.isInteger(quantity)) {
        return res.status(400).json({
          success: false,
          message: 'Quantity must be a positive integer',
          data: null
        });
      }

      const cartItem = await this.cartService.addToCart(accountId, ticketTypeId, quantity);

      res.status(201).json({
        success: true,
        message: 'Item added to cart successfully',
        data: cartItem
      });
    } catch (error) {
      console.error('Error adding to cart:', error);

      // Handle specific error cases
      if (error.message.includes('not found') || error.message.includes('Not enough tickets') || error.message.includes('Maximum')) {
        return res.status(400).json({
          success: false,
          message: error.message,
          data: null
        });
      }

      res.status(500).json({
        success: false,
        message: error.message || 'Failed to add item to cart',
        data: null
      });
    }
  };

  // Update cart item quantity
  updateCartItemQuantity = async (req, res) => {
    try {
      const accountId = req.user.accountId || req.user.id;
      const { cartId } = req.params;
      const { quantity } = req.body;

      if (!accountId) {
        return res.status(401).json({
          success: false,
          message: 'User authentication required',
          data: null
        });
      }

      if (!cartId || isNaN(parseInt(cartId))) {
        return res.status(400).json({
          success: false,
          message: 'Valid cart ID is required',
          data: null
        });
      }

      if (!quantity || quantity <= 0 || !Number.isInteger(quantity)) {
        return res.status(400).json({
          success: false,
          message: 'Quantity must be a positive integer',
          data: null
        });
      }

      const updatedCartItem = await this.cartService.updateCartItemQuantity(accountId, parseInt(cartId), quantity);

      res.status(200).json({
        success: true,
        message: 'Cart item updated successfully',
        data: updatedCartItem
      });
    } catch (error) {
      console.error('Error updating cart item:', error);

      // Handle specific error cases
      if (error.message.includes('not found') || error.message.includes('Not enough tickets') || error.message.includes('Maximum')) {
        return res.status(400).json({
          success: false,
          message: error.message,
          data: null
        });
      }

      res.status(500).json({
        success: false,
        message: error.message || 'Failed to update cart item',
        data: null
      });
    }
  };

  // Remove item from cart
  removeFromCart = async (req, res) => {
    try {
      const accountId = req.user.accountId || req.user.id;
      const { cartId } = req.params;

      if (!accountId) {
        return res.status(401).json({
          success: false,
          message: 'User authentication required',
          data: null
        });
      }

      if (!cartId || isNaN(parseInt(cartId))) {
        return res.status(400).json({
          success: false,
          message: 'Valid cart ID is required',
          data: null
        });
      }

      const result = await this.cartService.removeFromCart(accountId, parseInt(cartId));

      res.status(200).json({
        success: true,
        message: 'Item removed from cart successfully',
        data: result
      });
    } catch (error) {
      console.error('Error removing from cart:', error);

      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
          data: null
        });
      }

      res.status(500).json({
        success: false,
        message: error.message || 'Failed to remove item from cart',
        data: null
      });
    }
  };

  // Clear entire cart
  clearCart = async (req, res) => {
    try {
      const accountId = req.user.accountId || req.user.id;

      if (!accountId) {
        return res.status(401).json({
          success: false,
          message: 'User authentication required',
          data: null
        });
      }

      const result = await this.cartService.clearCart(accountId);

      res.status(200).json({
        success: true,
        message: 'Cart cleared successfully',
        data: result
      });
    } catch (error) {
      console.error('Error clearing cart:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to clear cart',
        data: null
      });
    }
  };

  // Get cart summary
  getCartSummary = async (req, res) => {
    try {
      const accountId = req.user.accountId || req.user.id;

      if (!accountId) {
        return res.status(401).json({
          success: false,
          message: 'User authentication required',
          data: null
        });
      }

      const summary = await this.cartService.getCartSummary(accountId);

      res.status(200).json({
        success: true,
        message: 'Cart summary fetched successfully',
        data: summary
      });
    } catch (error) {
      console.error('Error getting cart summary:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to get cart summary',
        data: null
      });
    }
  };
}

module.exports = CartController;
