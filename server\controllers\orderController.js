const OrderService = require('../services/orderService');

class OrderController {
  constructor() {
    this.orderService = OrderService.getInstance();
  }

  // Get user's orders
  getUserOrders = async (req, res) => {
    try {
      const { roleId, roleType } = req.user;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== 'user') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Only users can view orders.'
        });
      }

      const orders = await this.orderService.getUserOrders(roleId);

      res.status(200).json({
        success: true,
        message: 'Orders fetched successfully',
        data: {
          orders
        }
      });
    } catch (error) {
      console.error('Error fetching user orders:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch orders',
        data: null
      });
    }
  };

  // Get user's tickets (formatted for dashboard)
  getUserTickets = async (req, res) => {
    try {
      const { roleId, roleType } = req.user;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== 'user') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Only users can view tickets.'
        });
      }

      const tickets = await this.orderService.getUserTickets(roleId);

      res.status(200).json({
        success: true,
        message: 'Tickets fetched successfully',
        data: {
          tickets
        }
      });
    } catch (error) {
      console.error('Error fetching user tickets:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch tickets',
        data: null
      });
    }
  };

  // Get specific order details
  getOrderById = async (req, res) => {
    try {
      const { roleId, roleType } = req.user;
      const { orderId } = req.params;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== 'user') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Only users can view order details.'
        });
      }

      // Validate orderId
      const orderIdNum = parseInt(orderId);
      if (isNaN(orderIdNum)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid order ID'
        });
      }

      const order = await this.orderService.getOrderById(orderIdNum, roleId);

      res.status(200).json({
        success: true,
        message: 'Order details fetched successfully',
        data: {
          order
        }
      });
    } catch (error) {
      console.error('Error fetching order details:', error);
      
      if (error.message === 'Order not found') {
        return res.status(404).json({
          success: false,
          message: 'Order not found'
        });
      }

      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch order details',
        data: null
      });
    }
  };

  // Get user order statistics
  getUserOrderStats = async (req, res) => {
    try {
      const { roleId, roleType } = req.user;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== 'user') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Only users can view order statistics.'
        });
      }

      const stats = await this.orderService.getUserOrderStats(roleId);

      res.status(200).json({
        success: true,
        message: 'Order statistics fetched successfully',
        data: {
          stats
        }
      });
    } catch (error) {
      console.error('Error fetching order statistics:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch order statistics',
        data: null
      });
    }
  };
}

module.exports = OrderController;
