{"name": "server", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.10.1", "@supabase/supabase-js": "^2.50.2", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.8", "prisma": "^6.10.1", "uuid": "^11.1.0"}, "devDependencies": {"body-parser": "^2.2.0", "cookie-parser": "^1.4.7", "nodemon": "^3.1.10"}}