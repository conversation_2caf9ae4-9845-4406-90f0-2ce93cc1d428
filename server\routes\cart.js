const express = require('express');
const CartController = require('../controllers/cartController');
const { verifyTokenFromCookie } = require('../middleware/jwtCookieMiddleware');

const router = express.Router();
const cartController = new CartController();

// All cart routes require authentication
router.use(verifyTokenFromCookie);

// Get cart summary (total items, total price) - must come before parameterized routes
// GET /api/cart/summary
router.get('/summary', cartController.getCartSummary);

// Get user's cart items
// GET /api/cart
router.get('/', cartController.getCartItems);

// Add item to cart
// POST /api/cart
router.post('/', cartController.addToCart);

// Clear entire cart
// DELETE /api/cart
router.delete('/', cartController.clearCart);

// Update cart item quantity
// PUT /api/cart/:cartId
router.put('/:cartId', cartController.updateCartItemQuantity);

// Remove item from cart
// DELETE /api/cart/:cartId
router.delete('/:cartId', cartController.removeFromCart);

module.exports = router;
