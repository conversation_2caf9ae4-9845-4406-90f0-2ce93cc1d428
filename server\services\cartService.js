const { PrismaClient } = require('@prisma/client');

class CartService {
  constructor() {
    this.prisma = new PrismaClient();
  }

  // Helper method to get user_id from account_id
  async getUserIdFromAccountId(accountId) {
    const user = await this.prisma.users.findUnique({
      where: {
        account_id: accountId
      },
      select: {
        user_id: true
      }
    });

    if (!user) {
      throw new Error('User profile not found. Please complete your profile setup.');
    }

    return user.user_id;
  }

  // Get user's cart items
  async getCartItems(accountId) {
    try {
      const userId = await this.getUserIdFromAccountId(accountId);

      const cartItems = await this.prisma.cart.findMany({
        where: {
          user_id: userId
        },
        include: {
          tickettypes: {
            include: {
              events: {
                select: {
                  event_id: true,
                  title: true,
                  banner_image: true,
                  start_date: true,
                  end_date: true,
                  venue_name: true
                }
              },
              eventcategories: {
                select: {
                  category_id: true,
                  name: true,
                  category_type: true
                }
              }
            }
          }
        },
        orderBy: {
          created_at: 'desc'
        }
      });

      // Transform data to match frontend expectations
      const transformedItems = cartItems.map(item => ({
        cart_id: item.cart_id,
        eventId: item.tickettypes.events.event_id,
        eventTitle: item.tickettypes.events.title,
        eventImage: item.tickettypes.events.banner_image,
        eventDate: item.tickettypes.events.start_date,
        eventEndDate: item.tickettypes.events.end_date,
        venue: item.tickettypes.events.venue_name,
        ticketTypeId: item.ticket_type_id,
        ticketType: item.tickettypes.name,
        ticketDescription: item.tickettypes.description,
        category: item.tickettypes.eventcategories?.name || 'General',
        categoryId: item.tickettypes.eventcategories?.category_id,
        price: parseFloat(item.tickettypes.price),
        quantity: item.quantity,
        maxPerOrder: item.tickettypes.max_per_order,
        quantityAvailable: item.tickettypes.quantity_available,
        createdAt: item.created_at,
        updatedAt: item.updated_at
      }));

      return transformedItems;
    } catch (error) {
      console.error('Error fetching cart items:', error);
      throw new Error('Failed to fetch cart items');
    }
  }

  // Add item to cart
  async addToCart(accountId, ticketTypeId, quantity = 1) {
    try {
      const userId = await this.getUserIdFromAccountId(accountId);

      // First, check if ticket type exists and has availability
      const ticketType = await this.prisma.tickettypes.findUnique({
        where: {
          ticket_type_id: ticketTypeId
        }
      });

      if (!ticketType) {
        throw new Error('Ticket type not found');
      }

      if (ticketType.quantity_available < quantity) {
        throw new Error('Not enough tickets available');
      }

      // Check if item already exists in cart
      const existingCartItem = await this.prisma.cart.findFirst({
        where: {
          user_id: userId,
          ticket_type_id: ticketTypeId
        }
      });

      let cartItem;

      if (existingCartItem) {
        // Update existing cart item
        const newQuantity = existingCartItem.quantity + quantity;

        // Check max per order limit
        if (ticketType.max_per_order && newQuantity > ticketType.max_per_order) {
          throw new Error(`Maximum ${ticketType.max_per_order} tickets allowed per order`);
        }

        // Check availability for new quantity
        if (ticketType.quantity_available < newQuantity) {
          throw new Error('Not enough tickets available');
        }

        cartItem = await this.prisma.cart.update({
          where: {
            cart_id: existingCartItem.cart_id
          },
          data: {
            quantity: newQuantity,
            updated_at: new Date()
          }
        });
      } else {
        // Check max per order limit for new item
        if (ticketType.max_per_order && quantity > ticketType.max_per_order) {
          throw new Error(`Maximum ${ticketType.max_per_order} tickets allowed per order`);
        }

        // Create new cart item
        cartItem = await this.prisma.cart.create({
          data: {
            user_id: userId,
            ticket_type_id: ticketTypeId,
            quantity: quantity
          }
        });
      }

      return cartItem;
    } catch (error) {
      console.error('Error adding to cart:', error);
      throw error;
    }
  }

  // Update cart item quantity
  async updateCartItemQuantity(accountId, cartId, quantity) {
    try {
      const userId = await this.getUserIdFromAccountId(accountId);

      // Verify cart item belongs to user
      const cartItem = await this.prisma.cart.findFirst({
        where: {
          cart_id: cartId,
          user_id: userId
        },
        include: {
          tickettypes: true
        }
      });

      if (!cartItem) {
        throw new Error('Cart item not found');
      }

      // Validate quantity
      if (quantity <= 0) {
        throw new Error('Quantity must be greater than 0');
      }

      // Check max per order limit
      if (cartItem.tickettypes.max_per_order && quantity > cartItem.tickettypes.max_per_order) {
        throw new Error(`Maximum ${cartItem.tickettypes.max_per_order} tickets allowed per order`);
      }

      // Check availability
      if (cartItem.tickettypes.quantity_available < quantity) {
        throw new Error('Not enough tickets available');
      }

      const updatedCartItem = await this.prisma.cart.update({
        where: {
          cart_id: cartId
        },
        data: {
          quantity: quantity,
          updated_at: new Date()
        }
      });

      return updatedCartItem;
    } catch (error) {
      console.error('Error updating cart item:', error);
      throw error;
    }
  }

  // Remove item from cart
  async removeFromCart(accountId, cartId) {
    try {
      const userId = await this.getUserIdFromAccountId(accountId);

      // Verify cart item belongs to user
      const cartItem = await this.prisma.cart.findFirst({
        where: {
          cart_id: cartId,
          user_id: userId
        }
      });

      if (!cartItem) {
        throw new Error('Cart item not found');
      }

      await this.prisma.cart.delete({
        where: {
          cart_id: cartId
        }
      });

      return { success: true };
    } catch (error) {
      console.error('Error removing from cart:', error);
      throw error;
    }
  }

  // Clear entire cart for user
  async clearCart(accountId) {
    try {
      const userId = await this.getUserIdFromAccountId(accountId);

      await this.prisma.cart.deleteMany({
        where: {
          user_id: userId
        }
      });

      return { success: true };
    } catch (error) {
      console.error('Error clearing cart:', error);
      throw new Error('Failed to clear cart');
    }
  }

  // Get cart summary (total items, total price)
  async getCartSummary(accountId) {
    try {
      const userId = await this.getUserIdFromAccountId(accountId);

      const cartItems = await this.prisma.cart.findMany({
        where: {
          user_id: userId
        },
        include: {
          tickettypes: {
            select: {
              price: true
            }
          }
        }
      });

      const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);
      const totalPrice = cartItems.reduce((sum, item) => {
        return sum + (parseFloat(item.tickettypes.price) * item.quantity);
      }, 0);

      return {
        totalItems,
        totalPrice: parseFloat(totalPrice.toFixed(2)),
        itemCount: cartItems.length
      };
    } catch (error) {
      console.error('Error getting cart summary:', error);
      throw new Error('Failed to get cart summary');
    }
  }
}

module.exports = CartService;
