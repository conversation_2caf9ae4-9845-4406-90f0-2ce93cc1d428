const prismaService = require('../lib/prisma');

class EventService {
  constructor() {
    this.prisma = prismaService.getInstance();
  }

  // Get all events (no server-side filtering for frontend performance)
  async getAllEvents() {
    try {
      // Get all events with related data - no filtering on server side
      const events = await this.prisma.events.findMany({
        include: {
          genres: true,
          locations: true,
          organizers: {
            select: {
              organizer_id: true,
              organization_name: true,
              phone_number: true
            }
          },
          eventartists: {
            include: {
              artists: true
            }
          },
          tickettypes: {
            select: {
              ticket_type_id: true,
              name: true,
              price: true,
              quantity_available: true,
              description: true
            }
          }
        },
        orderBy: {
          start_date: 'asc'
        }
      });

      return {
        events: events.map(this.formatEventResponse),
        total: events.length
      };
    } catch (error) {
      throw new Error(`Failed to fetch events: ${error.message}`);
    }
  }

  // Get event by ID
  async getEventById(eventId) {
    try {
      const event = await this.prisma.events.findUnique({
        where: { event_id: parseInt(eventId) },
        include: {
          genres: true,
          locations: true,
          organizers: {
            select: {
              organizer_id: true,
              organization_name: true,
              phone_number: true
            }
          },

          tickettypes: {
            select: {
              ticket_type_id: true,
              name: true,
              price: true,
              quantity_available: true,
              description: true
            }
          },
          eventcategories: true
        }
      });

      if (!event) {
        throw new Error('Event not found');
      }

      return this.formatEventResponse(event);
    } catch (error) {
      throw new Error(`Failed to fetch event: ${error.message}`);
    }
  }

  // Get events by organizer
  async getEventsByOrganizer(organizerId, filters = {}) {
    try {
      const { page = 1, limit = 12 } = filters;
      const skip = (page - 1) * limit;
      const take = parseInt(limit);

      const events = await this.prisma.events.findMany({
        where: { organizer_id: parseInt(organizerId) },
        include: {
          genres: true,
          locations: true,
          organizers: {
            select: {
              organizer_id: true,
              organization_name: true,
              phone_number: true
            }
          },
          tickettypes: {
            select: {
              ticket_type_id: true,
              name: true,
              price: true,
              quantity_available: true,
              description: true
            }
          }
        },
        skip,
        take,
        orderBy: { start_date: 'asc' }
      });

      const totalCount = await this.prisma.events.count({
        where: { organizer_id: parseInt(organizerId) }
      });

      return {
        events: events.map(this.formatEventResponse),
        pagination: {
          page: parseInt(page),
          limit: take,
          total: totalCount,
          totalPages: Math.ceil(totalCount / take)
        }
      };
    } catch (error) {
      throw new Error(`Failed to fetch organizer events: ${error.message}`);
    }
  }

  // Get events by status (live, upcoming, past)
  async getEventsByStatus(status, filters = {}) {
    try {
      const {
        genreId,
        locationId,
        search,
        page = 1,
        limit = 12,
        sortBy = 'start_date',
        sortOrder = 'asc'
      } = filters;

      const skip = (page - 1) * limit;
      const take = parseInt(limit);
      const now = new Date();

      // Build where clause based on status
      const where = {};

      // Add status-based filtering
      switch (status) {
        case 'live':
          // Event is live when current time is between tickets_sale_start and tickets_sale_end
          where.AND = [
            { tickets_sale_start: { lte: now } },
            { tickets_sale_end: { gte: now } }
          ];
          break;
        case 'upcoming':
          // Event is upcoming when current date is less than tickets_sale_start
          where.tickets_sale_start = { gt: now };
          break;
        case 'past':
          // Event is past when current time is greater than event end_date
          where.end_date = { lt: now };
          break;
        default:
          throw new Error('Invalid status. Must be one of: live, upcoming, past');
      }

      // Add other filters
      if (genreId) {
        where.genre_id = parseInt(genreId);
      }

      if (locationId) {
        where.location_id = parseInt(locationId);
      }

      if (search) {
        const searchCondition = {
          OR: [
            { title: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
            { venue_name: { contains: search, mode: 'insensitive' } }
          ]
        };

        if (where.AND) {
          where.AND.push(searchCondition);
        } else {
          where.AND = [searchCondition];
        }
      }

      // Get events with related data
      const events = await this.prisma.events.findMany({
        where,
        include: {
          genres: true,
          locations: true,
          organizers: {
            select: {
              organizer_id: true,
              organization_name: true,
              phone_number: true
            }
          },
          eventartists: {
            include: {
              artists: true
            }
          },
          tickettypes: {
            select: {
              ticket_type_id: true,
              name: true,
              price: true,
              quantity_available: true,
              description: true
            }
          }
        },
        skip,
        take,
        orderBy: {
          [sortBy]: sortOrder
        }
      });

      // Get total count for pagination
      const totalCount = await this.prisma.events.count({ where });

      return {
        events: events.map(this.formatEventResponse),
        pagination: {
          page: parseInt(page),
          limit: take,
          total: totalCount,
          totalPages: Math.ceil(totalCount / take)
        }
      };
    } catch (error) {
      throw new Error(`Failed to fetch ${status} events: ${error.message}`);
    }
  }

  // Get all genres
  async getAllGenres() {
    try {
      const genres = await this.prisma.genres.findMany({
        orderBy: { name: 'asc' }
      });
      return genres;
    } catch (error) {
      throw new Error(`Failed to fetch genres: ${error.message}`);
    }
  }

  // Get all locations
  async getAllLocations() {
    try {
      const locations = await this.prisma.locations.findMany({
        orderBy: { city: 'asc' }
      });
      return locations;
    } catch (error) {
      throw new Error(`Failed to fetch locations: ${error.message}`);
    }
  }

  // Format event response
  formatEventResponse(event) {
    return {
      id: event.event_id,
      title: event.title,
      description: event.description,
      startDate: event.start_date,
      endDate: event.end_date,
      startTime: event.start_time,
      bannerImage: event.banner_image,
      venueName: event.venue_name,
      status: event.status,
      ticketsSaleStart: event.tickets_sale_start,
      ticketsSaleEnd: event.tickets_sale_end,
      eventPolicy: event.event_policy,
      createdAt: event.created_at,
      updatedAt: event.updated_at,
      genre: event.genres ? {
        id: event.genres.genre_id,
        name: event.genres.name,
        icon: event.genres.icon
      } : null,
      location: event.locations ? {
        id: event.locations.location_id,
        city: event.locations.city,
        venueName: event.locations.venue_name,
        address: event.locations.address,
        mapLink: event.locations.map_link
      } : null,
      organizer: event.organizers ? {
        id: event.organizers.organizer_id,
        name: event.organizers.organization_name,
        phone: event.organizers.phone_number
      } : null,
      artists: event.eventartists ? event.eventartists.map(ea => ({
        id: ea.artists.artist_id,
        name: ea.artists.name,
        bio: ea.artists.bio,
        image: ea.artists.image
      })) : [],
      ticketTypes: event.tickettypes ? event.tickettypes.map(tt => ({
        id: tt.ticket_type_id,
        name: tt.name,
        description: tt.description,
        price: parseFloat(tt.price),
        quantity: tt.quantity_available,
        available: tt.quantity_available
      })) : [],
      categories: event.eventcategories ? event.eventcategories.map(ec => ({
        id: ec.category_id,
        name: ec.name,
        description: ec.description,
        type: ec.category_type
      })) : []
    };
  }

  // Close Prisma connection
  async disconnect() {
    await this.prisma.$disconnect();
  }
}

module.exports = EventService;
