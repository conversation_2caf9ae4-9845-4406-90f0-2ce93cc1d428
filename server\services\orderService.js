const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

class OrderService {
  static instance = null;

  static getInstance() {
    if (!OrderService.instance) {
      OrderService.instance = new OrderService();
    }
    return OrderService.instance;
  }

  // Get all orders for a user with ticket details
  async getUserOrders(userId) {
    try {
      const orders = await prisma.orders.findMany({
        where: {
          user_id: userId,
           // Only show completed orders
        },
        include: {
          orderitems: {
            include: {
              tickettypes: {
                include: {
                  events: {
                    include: {
                      locations: true,
                      genres: true
                    }
                  }
                }
              }
            }
          },
          tickets: true
        },
        orderBy: {
          created_at: 'desc'
        }
      });

      return orders;
    } catch (error) {
      console.error('Error fetching user orders:', error);
      throw new Error('Failed to fetch user orders');
    }
  }

  // Get user tickets with event details (formatted for dashboard)
  async getUserTickets(userId) {
    try {
      const tickets = await prisma.tickets.findMany({
        where: {
          orders: {
            user_id: userId,
            payment_status: 'completed'
          }
        },
        include: {
          orderitems: true,
          tickettypes: {
            include: {
              events: {
                include: {
                  locations: true,
                  genres: true
                }
              }
            }
          }
        },
        orderBy: {
          created_at: 'desc'
        }
      });

      // Format tickets for frontend
      const formattedTickets = tickets.map(ticket => {
        const event = ticket.tickettypes.events;
        const location = event.locations;
        
        return {
          id: ticket.ticket_id,
          eventId: event.event_id,
          eventTitle: event.title,
          eventImage: event.banner_image,
          eventDate: event.event_date,
          eventTime: event.event_time,
          eventLocation: {
            venue: location?.venue_name || 'TBD',
            city: location?.city || 'TBD',
            address: location?.address || 'TBD'
          },
          ticketType: ticket.tickettypes.name,
          price: parseFloat(ticket.tickettypes.price),
          purchaseDate: ticket.orders.created_at,
          qrCode: ticket.qr_code,
          isValidated: ticket.is_validated,
          validationTime: ticket.validation_time,
          ticketPdf: ticket.user_ticketpdf
        };
      });

      return formattedTickets;
    } catch (error) {
      console.error('Error fetching user tickets:', error);
      throw new Error('Failed to fetch user tickets');
    }
  }

  // Get order by ID (for detailed view)
  async getOrderById(orderId, userId) {
    try {
      const order = await prisma.orders.findFirst({
        where: {
          order_id: orderId,
          user_id: userId
        },
        include: {
          orderitems: {
            include: {
              tickettypes: {
                include: {
                  events: {
                    include: {
                      locations: true,
                      genres: true
                    }
                  }
                }
              }
            }
          },
          tickets: true,
          users: {
            select: {
              first_name: true,
              last_name: true,
              phone_number: true
            }
          }
        }
      });

      if (!order) {
        throw new Error('Order not found');
      }

      return order;
    } catch (error) {
      console.error('Error fetching order:', error);
      throw new Error('Failed to fetch order details');
    }
  }

  // Get order statistics for user
  async getUserOrderStats(userId) {
    try {
      const stats = await prisma.orders.aggregate({
        where: {
          user_id: userId,
          payment_status: 'completed'
        },
        _count: {
          order_id: true
        },
        _sum: {
          total_amount: true
        }
      });

      const ticketCount = await prisma.tickets.count({
        where: {
          orders: {
            user_id: userId,
            payment_status: 'completed'
          }
        }
      });

      return {
        totalOrders: stats._count.order_id || 0,
        totalSpent: parseFloat(stats._sum.total_amount || 0),
        totalTickets: ticketCount
      };
    } catch (error) {
      console.error('Error fetching user order stats:', error);
      throw new Error('Failed to fetch order statistics');
    }
  }
}

module.exports = OrderService;
